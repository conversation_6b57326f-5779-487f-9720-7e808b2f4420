"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  BarChart3, 
  TrendingUp, 
  Building2, 
  Calendar,
  Award,
  Users,
  Database,
  Activity
} from "lucide-react";

interface StatsData {
  overview: {
    totalCount: number;
    activeCount: number;
    recentUpdates: number;
    inactiveCount: number;
  };
  categories: Array<{ name: string; count: number }>;
  companies: Array<{ name: string; count: number }>;
  managementTypes: Array<{ name: string; count: number }>;
  yearlyApprovals: Array<{ year: number; count: number }>;
  specialCategories: {
    innovative: number;
    clinicalNeed: number;
    childrenSpecific: number;
    rareDisease: number;
  };
}

interface StatsPanelProps {
  database: string;
  className?: string;
}

export default function StatsPanel({ database, className ="" }: StatsPanelProps) {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 确保组件卸载时清理所有状态
  useEffect(() => {
    return () => {
      // 清理函数，确保组件卸载时不会留下残留状态
    };
  }, []);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/stats/${database}`);
        const result = await response.json();
        
        if (result.success) {
          setStats(result.data);
        } else {
          setError(result.error || '获取统计数据失败');
        }
      } catch (__err) {
        setError('网络错误，请稍后重试');
        console.error('Failed to fetch stats:', __err);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [database]);

  if (loading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-20" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !stats) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <p className="text-red-500">{error || '无法加载统计数据'}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 概览统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总数据量</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.overview.totalCount.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              活跃: {stats.overview.activeCount.toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">最近更新</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.overview.recentUpdates.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              近30天更新
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">创新产品</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.specialCategories.innovative.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              创新医疗器械
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">临床急需</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.specialCategories.clinicalNeed.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              临床急需产品
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 详细统计 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 产品类别统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              产品类别分布
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {stats.categories.slice(0, 5).map((category, _index) => (
                <div key={category.name} className="flex items-center justify-between">
                  <span className="text-sm truncate flex-1">{category.name}</span>
                  <Badge variant="secondary" className="ml-2">
                    {category.count.toLocaleString()}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 企业统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              主要企业
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {stats.companies.slice(0, 5).map((company, _index) => (
                <div key={company.name} className="flex items-center justify-between">
                  <span className="text-sm truncate flex-1">{company.name}</span>
                  <Badge variant="outline" className="ml-2">
                    {company.count.toLocaleString()}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 管理类别统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              管理类别
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {stats.managementTypes.map((type, _index) => (
                <div key={type.name} className="flex items-center justify-between">
                  <span className="text-sm">{type.name}</span>
                  <Badge variant="default" className="ml-2">
                    {type.count.toLocaleString()}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 年度批准统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              年度批准趋势
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {stats.yearlyApprovals.slice(0, 5).map((year, _index) => (
                <div key={year.year} className="flex items-center justify-between">
                  <span className="text-sm">{year.year}年</span>
                  <Badge variant="outline" className="ml-2">
                    {year.count.toLocaleString()}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 特殊类别统计 */}
      <Card>
        <CardHeader>
          <CardTitle>特殊类别统计</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {stats.specialCategories.innovative}
              </div>
              <div className="text-sm text-muted-foreground">创新产品</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {stats.specialCategories.clinicalNeed}
              </div>
              <div className="text-sm text-muted-foreground">临床急需</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {stats.specialCategories.childrenSpecific}
              </div>
              <div className="text-sm text-muted-foreground">儿童专用</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {stats.specialCategories.rareDisease}
              </div>
              <div className="text-sm text-muted-foreground">罕见病</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
