"use client";

import { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, Search, Menu, User, Crown, Building } from "lucide-react";
import { useAuth } from "@/lib/auth";
import { canAccessDatabaseSync, getDatabaseConfigs } from "@/lib/permissions";

interface NavigationProps {
  showSearch?: boolean;
}

export default function Navigation({ showSearch = false }: NavigationProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [searchQuery, setSearchQuery] = useState("");
  const [databaseConfigs, setDatabaseConfigs] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);
  const { user, logout } = useAuth();

  // 获取数据库配置
  useEffect(() => {
    const fetchConfigs = async () => {
      try {
        const configs = await getDatabaseConfigs();
        setDatabaseConfigs(configs);
      } catch (__error) {
        console.error('Failed to fetch database configs:', __error);
      } finally {
        setLoading(false);
      }
    };

    fetchConfigs();
  }, []);

  // 渲染数据库链接的辅助函数
  const renderDatabaseLink = (code: string, name: string, accessLevel: string) => {
    const hasAccess = canAccessDatabaseSync(user?.membershipType || null, code, accessLevel);

    return (
      <DropdownMenuItem key={code} asChild>
        <Link
          href={`/data/list/${code}`}
          className={`flex items-center justify-between w-full ${
            !hasAccess ? 'text-gray-400' : ''
          }`}
        >
          <span>{name}</span>
          {accessLevel ==="free" ? (
            <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs">
              Free
            </Badge>
          ) : !hasAccess ? (
            <Badge variant="secondary" className={
              accessLevel ==="enterprise" ? "bg-purple-100 text-purple-800 text-xs"
                : "bg-yellow-100 text-yellow-800 text-xs"
            }>
              {accessLevel ==="enterprise" ? (
                <Building className="h-3 w-3 mr-1" />
              ) : (
                <Crown className="h-3 w-3 mr-1" />
              )}
              {accessLevel ==="premium" ? 'Premium' : 'Enterprise'}
            </Badge>
          ) : null}
        </Link>
      </DropdownMenuItem>
    );
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      // 导航栏搜索统一跳转到首页进行全局搜索
      router.push(`/?q=${encodeURIComponent(searchQuery.trim())}`);
      // 清空搜索框
      setSearchQuery('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <nav className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="text-blue-600 font-bold text-2xl">DataQuery</div>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center">
            <Button variant="ghost" size="sm">
              <Menu className="h-6 w-6" />
            </Button>
          </div>

          {/* Desktop Navigation Menu */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-700 hover:text-blue-600 font-medium">
              Home
            </Link>

            {/* 动态生成分类菜单 */}
            {loading ? (
              <div className="text-gray-400">Loading...</div>
            ) : (
              // 获取所有唯一的分类并按 categoryOrder 排序后动态生成菜单
              (() => {
                // 创建分类到 categoryOrder 的映射
                const categoryToOrder = new Map<string, number>();
                Object.values(databaseConfigs).forEach((config: Record<string, unknown>) => {
                  if (!categoryToOrder.has(config.category as string)) {
                    // 从配置中获取 categoryOrder，如果没有则使用 sortOrder 作为备选
                    const categoryOrder = (config.categoryOrder as number) || (config.sortOrder as number) || 99;
                    categoryToOrder.set(config.category as string, categoryOrder);
                  }
                });

                // 获取所有唯一分类并按 categoryOrder 排序
                return Array.from(new Set(Object.values(databaseConfigs).map((config: Record<string, unknown>) => config.category as string)))
                  .sort((a, b) => {
                    const orderA = categoryToOrder.get(a) || 99;
                    const orderB = categoryToOrder.get(b) || 99;
                    return orderA - orderB;
                  })
                  .map((category: string) => (
                    <DropdownMenu key={category}>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="text-gray-700 hover:text-blue-600 font-medium">
                          {category} <ChevronDown className="ml-1 h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        {/* 显示该分类下的所有数据库，按 orderInCategory 排序 */}
                        {Object.entries(databaseConfigs)
                          .filter(([code, config]: [string, any]) => config.category === category)
                          .sort(([, configA], [, configB]) => {
                            const orderA = configA.orderInCategory || configA.sortOrder || 0;
                            const orderB = configB.orderInCategory || configB.sortOrder || 0;
                            return orderA - orderB;
                          })
                          .map(([code, config]: [string, any]) =>
                            renderDatabaseLink(code, config.name, config.accessLevel)
                          )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  ));
              })()
            )}
          </div>

          {/* Search Bar (for list pages) */}
          {showSearch && (
            <div className="hidden md:flex items-center space-x-4">
              <div className="relative">
                <Input
                  type="text" placeholder="Enter product name, company name, application number..." className="w-80 lg:w-96 pr-10" value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={handleKeyPress}
                />
                <Button
                  variant="ghost" size="sm" className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-gray-100" onClick={handleSearch}
                >
                  <Search className="h-4 w-4 text-gray-400" />
                </Button>
              </div>
            </div>
          )}

          {/* User Menu / Login */}
          <div className="flex items-center space-x-4">
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center space-x-2">
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4" />
                      <span className="hidden md:inline">{user.name}</span>
                      {user.membershipType ==="premium" && (
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          <Crown className="h-3 w-3 mr-1" />
                          Premium
                        </Badge>
                      )}
                      {user.membershipType ==="enterprise" && (
                        <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                          <Building className="h-3 w-3 mr-1" />
                          Enterprise
                        </Badge>
                      )}
                    </div>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link href="/profile">Profile</Link>
                  </DropdownMenuItem>
                  {user.membershipType ==="free" && (
                    <DropdownMenuItem asChild>
                      <Link href="/upgrade" className="text-blue-600">
                        <Crown className="h-4 w-4 mr-2" />
                        Upgrade Membership
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={logout} className="text-red-600">
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center space-x-2">
                <Button variant="ghost" asChild>
                  <Link href="/login">Login</Link>
                </Button>
                <Button asChild>
                  <Link href="/register">Register</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
