import { esClient } from '@/lib/elasticsearch';
import { db } from '@/lib/prisma';

/**
 * 根据 MedicalDevice 表将数据批量写入 Elasticsearch。
 *
 * 索引命名约定：db-<databaseCode>
 */
async function ensureIndex(databaseCode: string) {
  const indexName = `db-${databaseCode.toLowerCase()}`;
  const exists = await esClient.indices.exists({ index: indexName });
  if (!exists) {
    // 基础 mapping，可按需扩充
    await esClient.indices.create({
      index: indexName,
      settings: {
        analysis: {
          analyzer: {
            zh_analyzer: {
              type: 'custom' as any,
            },
          },
        },
      },
      mappings: {
        dynamic_templates: [
          {
            strings_as_text: {
              match_mapping_type: 'string',
              mapping: {
                type: 'text',
                analyzer: 'zh_analyzer',
                fields: {
                  keyword: { type: 'keyword' },
                  substr: { type: 'wildcard' },
                },
              },
            },
          },
        ],
        properties: {
          id: { type: 'keyword' },
          database: { type: 'keyword' },
          approvalDate: { type: 'date' },
          validUntil: { type: 'date' },
        },
      },
    });
    console.error(`Created index ${indexName}`);
  }
}

async function run() {
  // 从命令行参数获取表名
  const tableName = process.argv[2] || 'medicalDevice';
  console.log(`正在索引表: ${tableName}`);

  // 获取表的动态引用
  const table = (db as any)[tableName];
  if (!table) {
    console.error(`❌ 表 ${tableName} 不存在`);
    process.exit(1);
  }

  // 取所有活跃的数据
  const batchSize = 1000;
  let skip = 0;
  while (true) {
    const records = await table.findMany({
      skip,
      take: batchSize,
      orderBy: { id: 'asc' },
    });
    if (records.length === 0) break;

    // 按 databaseCode 分组，确保索引存在
    const dbCodes = Array.from(new Set(records.map((r: any) => r.database)));
    for (const code of dbCodes) {
      await ensureIndex(String(code));
    }

    const operations: unknown[] = [];
    for (const doc of records) {
      const indexName = `db-${doc.database.toLowerCase()}`;
      operations.push({ index: { _index: indexName, _id: doc.id } });
      // 动态构建文档，包含所有字段
      const docData: any = {};
      for (const [key, value] of Object.entries(doc)) {
        docData[key] = value;
      }
      operations.push(docData);
    }

    const bulkRes = await esClient.bulk({ body: operations, refresh: true });
    if (bulkRes.errors) {
      const problematic = (bulkRes.items || []).filter((item: any) => {
        return item.index && item.index.error;
      });
      console.error('Bulk errors:', problematic);
    } else {
      console.error(`Indexed ${records.length} documents (skip=${skip})`);
    }

    skip += records.length;
  }

  console.error('✅ 完成索引导入');
  process.exit(0);
}

run().catch((err) => {
  console.error(err);
  process.exit(1);
}); 