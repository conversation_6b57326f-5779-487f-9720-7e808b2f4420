'use client';

import { useState, useEffect, useCallback } from 'react';

interface ApiResponse {
  success: boolean;
  data?: unknown;
  error?: string;
}

export default function SimpleTestPage() {
  const [data, setData] = useState<ApiResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.error('🔍 Starting simple test...');

        const response = await fetch('/api/config/databases');
        console.error('📡 Response status:', response.status);

        const result = await response.json();
        console.error('📋 Response data:', result);

        setData(result);
      } catch (__err) {
        console.error('❌ Error:', __err);
        setError(__err instanceof Error ? __err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    // Delay execution to ensure component is fully mounted
    const timer = setTimeout(fetchData, 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Simple Test Page</h1>

      {loading && (
        <div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
          <p>Loading...</p>
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong>Error:</strong> {error}
        </div>
      )}

      {!loading && !error && data && (
        <div>
          <h2 className="text-xl font-semibold mb-2">API Response</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
