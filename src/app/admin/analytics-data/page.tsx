"use client";

import { useState, useEffect, useCallback } from 'react';
import Navigation from '@/components/Navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Database, Search, Users, Clock } from 'lucide-react';

interface ActivityLog {
  id: string;
  eventType: string;
  database: string;
  path: string;
  queryParams: string;
  ip: string;
  createdAt: string;
  userId: string;
}

interface SearchAnalytics {
  id: string;
  database: string;
  searchType: string;
  searchQuery: string;
  searchFields: Record<string, unknown>;
  filters: Record<string, unknown>;
  resultsCount: number;
  searchTime: number;
  createdAt: string;
  userId: string;
}

interface AnalyticsStats {
  totalActivities: number;
  totalSearches: number;
  uniqueUsers: number;
  topDatabases: Array<{ database: string; count: number }>;
  topSearchQueries: Array<{ query: string; count: number }>;
  eventTypeStats: Array<{ eventType: string; count: number }>;
}

export default function AnalyticsDataPage() {
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
  const [searchAnalytics, setSearchAnalytics] = useState<SearchAnalytics[]>([]);
  const [stats, setStats] = useState<AnalyticsStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'activity' | 'search' | 'stats'>('stats');
  const [timeRange, setTimeRange] = useState('7d');

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      // 获取活动日志
      const activityResponse = await fetch('/api/analytics/activity-logs');
      if (activityResponse.ok) {
        const activityData = await activityResponse.json();
        setActivityLogs(activityData.data || []);
      }

      // 获取搜索分析
      const searchResponse = await fetch(`/api/analytics/search?action=analytics&timeRange=${timeRange}`);
      if (searchResponse.ok) {
        const searchData = await searchResponse.json();
        setSearchAnalytics(searchData.data?.searches || []);
      }

      // 获取统计数据
      const statsResponse = await fetch(`/api/analytics/search?action=summary&timeRange=${timeRange}`);
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data || null);
      }
    } catch (__error) {
      console.error('Failed to fetch analytics data:', __error);
    } finally {
      setLoading(false);
    }
  }, [timeRange]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US');
  };

  const getEventTypeBadge = (eventType: string) => {
    const colors: Record<string, string> = {
      'page_view': 'bg-blue-100 text-blue-800',
      'database_search': 'bg-green-100 text-green-800',
      'advanced_search': 'bg-purple-100 text-purple-800',
      'filter': 'bg-yellow-100 text-yellow-800',
      'export': 'bg-red-100 text-red-800',
      'detail_view': 'bg-indigo-100 text-indigo-800',
    };
    
    return (
      <Badge className={colors[eventType] || 'bg-gray-100 text-gray-800'}>
        {eventType || 'pageview'}
      </Badge>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900">Analytics数据查看</h1>
          <div className="flex gap-4">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1d">最近1天</SelectItem>
                <SelectItem value="7d">最近7天</SelectItem>
                <SelectItem value="30d">最近30天</SelectItem>
                <SelectItem value="90d">最近90天</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={fetchData} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="flex space-x-1 mb-6">
          {[
            { key: 'stats', label: '统计概览', icon: Database },
            { key: 'activity', label: '活动日志', icon: Users },
            { key: 'search', label: '搜索分析', icon: Search },
          ].map(({ key, label, icon: Icon }) => (
            <Button
              key={key}
              variant={activeTab === key ? 'default' : 'outline'}
              onClick={() => setActiveTab(key as any)}
              className="flex items-center gap-2">
              <Icon className="h-4 w-4" />
              {label}
            </Button>
          ))}
        </div>

        {/* 统计概览 */}
        {activeTab ==="stats" && stats && (
          <div className="space-y-6">
            {/* 概览卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">总搜索次数</CardTitle>
                  <Search className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalSearches}</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">独特查询词</CardTitle>
                  <Database className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.topSearchQueries.length}</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">平均搜索时间</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">N/A</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">搜索成功率</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">N/A</div>
                </CardContent>
              </Card>
            </div>

            {/* 热门搜索词 */}
            <Card>
              <CardHeader>
                <CardTitle>热门搜索词</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {stats.topSearchQueries.slice(0, 10).map((query, _index) => (
                    <div key={_index} className="flex justify-between items-center">
                      <span className="font-medium">"{query.query}"</span>
                      <Badge variant="secondary">{query.count}次</Badge>
                    </div>
                  ))}
                  {stats.topSearchQueries.length === 0 && (
                    <p className="text-gray-500">暂无搜索数据</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 数据库使用统计 */}
            <Card>
              <CardHeader>
                <CardTitle>数据库访问统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {stats.topDatabases.map((db, _index) => (
                    <div key={_index} className="flex justify-between items-center">
                      <span className="font-medium">{db.database}</span>
                      <Badge variant="outline">{db.count}次</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* 活动日志 */}
        {activeTab ==="activity" && (
          <Card>
            <CardHeader>
              <CardTitle>最近活动日志 (共{activityLogs.length}条)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activityLogs.slice(0, 50).map((log) => (
                  <div key={log.id} className="border-l-4 border-blue-500 pl-4 py-2">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getEventTypeBadge(log.eventType)}
                        <span className="font-medium">{log.database || '无数据库'}</span>
                      </div>
                      <span className="text-sm text-gray-500">{formatDate(log.createdAt)}</span>
                    </div>
                    <div className="text-sm text-gray-600">
                      <div>路径: {log.path}</div>
                      {log.queryParams && <div>查询参数: {log.queryParams}</div>}
                      <div>用户: {log.userId || '匿名'} | IP: {log.ip}</div>
                    </div>
                  </div>
                ))}
                {activityLogs.length === 0 && (
                  <p className="text-gray-500">暂无活动日志</p>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 搜索分析 */}
        {activeTab ==="search" && (
          <Card>
            <CardHeader>
              <CardTitle>搜索分析数据 (共{searchAnalytics.length}条)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {searchAnalytics.slice(0, 50).map((search) => (
                  <div key={search.id} className="border-l-4 border-green-500 pl-4 py-2">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge className="bg-green-100 text-green-800">{search.searchType}</Badge>
                        <span className="font-medium">{search.database}</span>
                      </div>
                      <span className="text-sm text-gray-500">{formatDate(search.createdAt)}</span>
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      {search.searchQuery && <div>搜索词: "{search.searchQuery}"</div>}
                      {search.searchFields && (
                        <div>搜索字段: {JSON.stringify(search.searchFields)}</div>
                      )}
                      {search.filters && (
                        <div>筛选条件: {JSON.stringify(search.filters)}</div>
                      )}
                      <div className="flex gap-4">
                        <span>结果: {search.resultsCount || 0}条</span>
                        <span>耗时: {search.searchTime || 0}ms</span>
                        <span>用户: {search.userId || '匿名'}</span>
                      </div>
                    </div>
                  </div>
                ))}
                {searchAnalytics.length === 0 && (
                  <p className="text-gray-500">暂无搜索分析数据</p>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
