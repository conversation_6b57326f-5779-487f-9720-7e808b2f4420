import { type NextRequest, NextResponse } from 'next/server';
import { getDatabaseConfig } from '@/lib/configCache';
import type { DatabaseConfig } from '@/lib/configCache';
import { getDynamicModel, validateDatabaseCode, isPrismaModel as isDynamicPrismaModel } from '@/lib/staticTableMappingService';
import { validatePaginationParams, buildPaginationResponse } from '@/lib/globalPagination';

export const dynamic = 'force-dynamic';

interface SearchCondition {
  id: string;
  field: string;
  operator: string;
  value: string | { from?: string; to?: string };
  logic?: 'AND' | 'OR' | 'NOT';
}

interface AdvancedSearchRequest {
  conditions: SearchCondition[];
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 构建复杂的 Prisma where 条件 - 重构版本
function buildAdvancedWhere(conditions: SearchCondition[], config: DatabaseConfig): Record<string, unknown> {
  if (conditions.length === 0) {
    return {};
  }

  // 如果只有一个条件，直接构建
  if (conditions.length === 1) {
    return buildSingleCondition(conditions[0], config);
  }

  // 多个条件需要根据逻辑操作符组合
  const result: Record<string, unknown> = {};
  const andConditions: Record<string, unknown>[] = [];
  const orConditions: Record<string, unknown>[] = [];
  const notConditions: Record<string, unknown>[] = [];

  conditions.forEach((condition, _index) => {
    const singleWhere = buildSingleCondition(condition, config);

    if (_index === 0) {
      // 第一个条件总是 AND
      andConditions.push(singleWhere);
    } else {
      switch (condition.logic) {
        case 'OR':
          orConditions.push(singleWhere);
          break;
        case 'NOT':
          notConditions.push(singleWhere);
          break;
        default: // 'AND'
          andConditions.push(singleWhere);
          break;
      }
    }
  });

  // 构建最终的 where 条件
  const finalConditions: Record<string, unknown>[] = [];

  if (andConditions.length > 0) {
    if (andConditions.length === 1) {
      finalConditions.push(andConditions[0]);
    } else {
      finalConditions.push({ AND: andConditions });
    }
  }

  if (orConditions.length > 0) {
    finalConditions.push({ OR: orConditions });
  }

  if (notConditions.length > 0) {
    finalConditions.push({ NOT: { OR: notConditions } });
  }

  if (finalConditions.length === 1) {
    return finalConditions[0];
  } else if (finalConditions.length > 1) {
    return { AND: finalConditions };
  }

  return {};
}

// 构建单个条件
function buildSingleCondition(condition: SearchCondition, config: DatabaseConfig): Record<string, unknown> {
  const fieldConfig = config.fields.find(f => f.fieldName === condition.field);
  const fieldName = condition.field;
  const operator = condition.operator;
  const value = condition.value;

  if (!fieldConfig) {
    return {};
  }

  // 处理日期类型
  if (fieldConfig.fieldType === 'date') {
    if (operator === 'between' && typeof value === 'object' && value.from && value.to) {
      return {
        [fieldName]: {
          gte: new Date(value.from),
          lte: new Date(value.to),
        }
      };
    } else if (typeof value === 'string' && value) {
      const date = new Date(value);
      switch (operator) {
        case 'equals':
          return { [fieldName]: date };
        case 'before':
          return { [fieldName]: { lt: date } };
        case 'after':
          return { [fieldName]: { gt: date } };
        default:
          return { [fieldName]: date };
      }
    }
    return {};
  }

  // 处理布尔类型
  if (fieldConfig.fieldType === 'boolean') {
    if (typeof value === 'string') {
      return { [fieldName]: value === 'true' };
    }
    return {};
  }

  // 处理文本类型
  if (typeof value === 'string' && value.trim()) {
    const trimmedValue = value.trim();

    // 处理N/A值
    if (trimmedValue === 'N/A') {
      switch (operator) {
        case 'equals':
          return {
            OR: [
              { [fieldName]: null },
              { [fieldName]: '' }
            ]
          };
        case 'notEquals':
          return {
            NOT: {
              OR: [
                { [fieldName]: null },
                { [fieldName]: '' }
              ]
            }
          };
        default:
          return {
            OR: [
              { [fieldName]: null },
              { [fieldName]: '' }
            ]
          };
      }
    }

    // 处理普通文本值
    switch (operator) {
      case 'contains':
        return { [fieldName]: { contains: trimmedValue, mode: 'insensitive' } };
      case 'equals':
        return { [fieldName]: { equals: trimmedValue, mode: 'insensitive' } };
      case 'startsWith':
        return { [fieldName]: { startsWith: trimmedValue, mode: 'insensitive' } };
      case 'endsWith':
        return { [fieldName]: { endsWith: trimmedValue, mode: 'insensitive' } };
      case 'notContains':
        return { NOT: { [fieldName]: { contains: trimmedValue, mode: 'insensitive' } } };
      case 'notEquals':
        return { NOT: { [fieldName]: { equals: trimmedValue, mode: 'insensitive' } } };
      default:
        return { [fieldName]: { contains: trimmedValue, mode: 'insensitive' } };
    }
  }

  return {};
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database: rawDatabase } = await params;
    
    // 统一转换为小写格式
    const database = rawDatabase.toLowerCase();

    // 验证数据库代码
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    // 权限检查 - 临时禁用用于测试
    /*
    const requiredLevel = getDatabaseAccessLevel(database);
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }
    */

    const body: AdvancedSearchRequest = await request.json();
    const { conditions, sortBy, sortOrder = 'desc' } = body;

    // 使用全局翻页配置（性能优化）
    const requestedPage = body.page || 1;
    const requestedLimit = body.limit || 0;

    const { page, limit } = validatePaginationParams(requestedPage, requestedLimit);

    // 获取配置
    const config: DatabaseConfig = await getDatabaseConfig(database);

    // 使用已获取的配置
    const visibleFields = config.fields.filter(f => f.isVisible).map(f => f.fieldName);
    const sortableFields = config.fields.filter(f => f.isSortable).map(f => f.fieldName);

    // 构建复杂查询条件 - 重构版本：不再需要database字段过滤
    const where = buildAdvancedWhere(conditions, config);

    // 构建排序条件
    const defaultSortField = sortableFields[0] || 'approvalDate';
    const orderBy = {
      [sortBy || defaultSortField]: sortOrder,
    };

    // 使用动态模型获取
    const model = await getDynamicModel(database);
    if (!isDynamicPrismaModel(model)) {
      return NextResponse.json(
        { success: false, error: '模型未找到或无效' },
        { status: 500 }
      );
    }

    // 构建 select 对象
    const select: Record<string, boolean> = {};
    visibleFields.forEach(f => { select[f] = true; });
    // 主键id始终返回，但不再返回database字段
    select['id'] = true;

    const data = await model.findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      select,
    }) as unknown[];
    
    const totalCount = await model.count({ where }) as number;
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      success: true,
      data,
      pagination: buildPaginationResponse(page, limit, totalCount),
      conditions,
      config,
      databaseInfo: {
        code: database,
        originalCode: rawDatabase,
      }
    });
    
  } catch (__error) {
    console.error('Advanced search API error:', __error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
