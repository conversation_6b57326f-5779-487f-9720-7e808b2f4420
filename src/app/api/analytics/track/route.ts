import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { getSession } from '@/lib/session';

// 获取客户端IP地址
function getClientIP(_request: NextRequest): string {
  const forwarded = _request.headers.get('x-forwarded-for');
  const realIP = _request.headers.get('x-real-ip');
  const remoteAddr = _request.headers.get('remote-addr');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  if (realIP) {
    return realIP.trim();
  }
  if (remoteAddr) {
    return remoteAddr.trim();
  }
  return '127.0.0.1';
}

// 从路径中提取数据库信息
function extractDatabaseFromPath(path: string): string | null {
  const matches = path.match(/\/data\/list\/([^\/\?]+)/);
  return matches ? matches[1] : null;
}

export async function POST(__request: NextRequest) {
  try {
    const body = await __request.json();
    const {
      event,
      data = {},
    } = body;

    if (!event) {
      return NextResponse.json(
        { success: false, error: 'Event type is required' },
        { status: 400 }
      );
    }

    // 获取用户会话信息
    const session = await getSession();
    const userId = (typeof session?.userId === 'string' ? session.userId : null);

    // 获取请求信息
    const ip = getClientIP(__request);
    const userAgent = __request.headers.get('user-agent') || undefined;
    const referer = __request.headers.get('referer') || undefined;

    // 从数据中提取信息
    const {
      path,
      queryParams = {},
      sessionId,
      timestamp,
      database: explicitDatabase,
      ...eventData
    } = data;

    // 尝试从路径或显式参数中获取数据库信息
    const database = explicitDatabase || 
                    extractDatabaseFromPath(path || '') || 
                    queryParams.database || 
                    undefined;

    // 记录活动日志
    await db.activityLog.create({
      data: {
        userId,
        ip,
        userAgent,
        path: path || __request.nextUrl.pathname,
        method: 'TRACK',
        queryParams: Object.keys(queryParams).length > 0 ? JSON.stringify(queryParams) : undefined,
        referer,
        database,
        eventType: event,
        sessionId,
      }
    });

    // 如果是特定事件，可以进行额外的处理
    switch (event) {
      case 'database_search':
        // 可以记录搜索统计
        break;
      case 'advanced_search':
        // 可以记录高级搜索使用情况
        break;
      case 'filter_applied':
        // 可以记录筛选器使用情况
        break;
      case 'data_export':
        // 可以记录数据导出情况
        break;
      case 'page_view':
        // 可以记录页面访问情况
        break;
      default:
        // 其他事件的通用处理
        break;
    }

    return NextResponse.json({ 
      success: true,
      message: 'Event tracked successfully'
    });

  } catch (__error) {
    console.error('Analytics tracking error:', __error);
    return NextResponse.json(
      { success: false, error: 'Failed to track event' },
      { status: 500 }
    );
  }
}

// 支持 GET 请求用于健康检查
export async function GET() {
  return NextResponse.json({
    success: true,
    message: 'Analytics tracking API is running',
    endpoints: {
      track: 'POST /api/analytics/track'
    }
  });
}
